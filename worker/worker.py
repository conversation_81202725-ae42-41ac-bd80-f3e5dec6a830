import json
import subprocess
import time
from loguru import logger
import schedule

import sys
sys.path.append("../")

from src.util.gg_chat_logging import post_message

ACCOUNT_CONFIG_PATH = '../account.json'

def read_config():
    with open(ACCOUNT_CONFIG_PATH, 'r') as f:
        return json.load(f)

def run_script(email: str, password: str):
    subprocess.run(["python", "src/main.py", "--email", email, "--password", password], check=True)

def point_farm():
    logger.info("Start point farm...")
    accounts = read_config()
    for acc in accounts:
        run_script(acc['email'], acc['password'])
    logger.info("Point farm finished")


def main():
    schedule.every().day.at("01:18").do(point_farm)
    logger.info("Start worker...")
    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == "__main__":
    main()
