#!/bin/bash

# Script to run Python main.py for each account in account.json
# Each account runs with 2 minutes delay between executions

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JSON_FILE="${SCRIPT_DIR}/account.json"
LOG_FILE="${SCRIPT_DIR}/run_accounts.log"
DELAY_MINUTES=2

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    case "$level" in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Check if required files exist
check_requirements() {
    if [ ! -f "$JSON_FILE" ]; then
        log "ERROR" "JSON file not found: $JSON_FILE"
        exit 1
    fi
    
    if [ ! -f "src/main.py" ]; then
        log "ERROR" "Python main.py not found in src/ directory"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log "ERROR" "jq is required but not installed"
        log "INFO" "Install jq: sudo apt-get install jq (Ubuntu/Debian) or brew install jq (macOS)"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        log "ERROR" "python3 is required but not installed"
        exit 1
    fi
}

# Update account status in JSON file
update_account_status() {
    local email="$1"
    local new_status="$2"
    local update_timestamp="$3"

    log "INFO" "Updating status for $email to $new_status"

    # Create backup
    cp "$JSON_FILE" "${JSON_FILE}.backup"

    # Update JSON file with status and optionally timestamp
    if [ "$new_status" = "success" ] && [ -n "$update_timestamp" ]; then
        jq --arg email "$email" --arg status "$new_status" --arg timestamp "$update_timestamp" \
           '(.[] | select(.email == $email) | .status) = $status | (.[] | select(.email == $email) | .last_success) = $timestamp' \
           "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    else
        jq --arg email "$email" --arg status "$new_status" \
           '(.[] | select(.email == $email) | .status) = $status' \
           "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"
    fi

    log "SUCCESS" "Updated status for $email to $new_status"
}

# Update account reward in JSON file
update_account_reward() {
    local email="$1"
    local new_reward="$2"

    log "INFO" "Updating reward for $email to $new_reward"

    # Update JSON file
    jq --arg email "$email" --argjson reward "$new_reward" \
       '(.[] | select(.email == $email) | .reward) = $reward' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"

    log "SUCCESS" "Updated reward for $email to $new_reward"
}

# Update account voucher status in JSON file
update_account_voucher() {
    local email="$1"
    local voucher_status="$2"

    log "INFO" "Updating voucher status for $email to $voucher_status"

    # Update JSON file
    jq --arg email "$email" --argjson voucher "$voucher_status" \
       '(.[] | select(.email == $email) | .voucher) = $voucher' \
       "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"

    log "SUCCESS" "Updated voucher status for $email to $voucher_status"
}

# Check if account needs daily reset (24 hours since last success)
check_daily_reset() {
    local email="$1"
    local last_success="$2"

    if [ "$last_success" = "null" ] || [ -z "$last_success" ]; then
        return 1  # No previous success, no reset needed
    fi

    local current_time=$(date +%s)
    local last_success_time=$(date -d "$last_success" +%s 2>/dev/null || echo "0")
    local time_diff=$((current_time - last_success_time))
    local twenty_four_hours=$((24 * 60 * 60))

    if [ $time_diff -ge $twenty_four_hours ]; then
        log "INFO" "Account $email is due for daily reset (24+ hours since last success)"
        return 0  # Reset needed
    else
        local hours_remaining=$(((twenty_four_hours - time_diff) / 3600))
        log "INFO" "Account $email not due for reset yet ($hours_remaining hours remaining)"
        return 1  # No reset needed
    fi
}

# Perform daily reset for accounts that have been successful for 24+ hours
perform_daily_reset() {
    log "INFO" "Checking for accounts that need daily reset..."

    local reset_count=0
    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.status)|\(.last_success)"' "$JSON_FILE")

    if [ -z "$account_data" ]; then
        log "ERROR" "No account data found for daily reset check"
        return 1
    fi

    while IFS='|' read -r email status last_success; do
        if [ "$status" = "success" ] && check_daily_reset "$email" "$last_success"; then
            update_account_status "$email" "process"
            reset_count=$((reset_count + 1))
            log "SUCCESS" "Reset $email from success to process (24+ hours elapsed)"
        fi
    done <<< "$account_data"

    if [ $reset_count -gt 0 ]; then
        log "SUCCESS" "Daily reset completed: $reset_count accounts reset to process status"
    else
        log "INFO" "No accounts needed daily reset"
    fi
}

# Run Python script for a single account
run_account() {
    local email="$1"
    local password="$2"
    local current_reward="$3"
    local current_status="$4"
    local current_voucher="$5"

    log "INFO" "Starting processing for account: $email"

    # Skip if account is not in process status
    if [ "$current_status" != "process" ]; then
        log "WARNING" "Skipping $email (status: $current_status)"
        return 0
    fi

    # Update status to processing (temporary status during execution)
    update_account_status "$email" "processing"

    # Run the Python script
    log "INFO" "Running: python3 src/main.py --email $email --password [HIDDEN]"

    local start_time=$(date +%s)
    local exit_code=0

    # Execute the Python command and capture output
    python3 src/main.py --email "$email" --password "$password" 2>&1 | tee -a "$LOG_FILE"
    exit_code=$?

    # Determine success based on exit code
    if [ $exit_code -eq 0 ]; then
        log "SUCCESS" "Python script completed successfully for $email (exit code: $exit_code)"

        # Update status to success with timestamp
        local success_timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
        update_account_status "$email" "success" "$success_timestamp"

        # Update reward (you can modify this logic as needed)
        local new_reward=$((current_reward + 10))
        update_account_reward "$email" "$new_reward"

        # Check if voucher was received (this would need to be determined by your Python script)
        # For now, we'll assume voucher status doesn't change unless explicitly updated
        # You can modify this logic based on your Python script's output

    else
        log "ERROR" "Python script failed for $email (exit code: $exit_code)"

        # Update status back to process so it can be retried
        update_account_status "$email" "process"
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    log "INFO" "Processing completed for $email in ${duration}s"

    return $exit_code
}

# Process all accounts with "process" status
process_all_accounts() {
    log "INFO" "Starting to process all accounts with 'process' status..."
    log "INFO" "Delay between accounts: ${DELAY_MINUTES} minutes"

    # Perform daily reset check first
    perform_daily_reset

    local total_accounts=0
    local processed_accounts=0
    local failed_accounts=0
    local skipped_accounts=0

    # Read accounts from JSON file
    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.password)|\(.reward)|\(.status)|\(.voucher)"' "$JSON_FILE")

    if [ -z "$account_data" ]; then
        log "ERROR" "No account data found or failed to read JSON file"
        return 1
    fi

    # Process each account
    local IFS=$'\n'
    for line in $account_data; do
        IFS='|' read -r email password reward status voucher <<< "$line"

        total_accounts=$((total_accounts + 1))

        log "INFO" "Account $total_accounts: $email (status: $status, reward: $reward, voucher: $voucher)"

        if [ "$status" = "process" ]; then
            if run_account "$email" "$password" "$reward" "$status" "$voucher"; then
                processed_accounts=$((processed_accounts + 1))
            else
                failed_accounts=$((failed_accounts + 1))
            fi

            # Add delay between accounts (except for the last one)
            local process_count
            process_count=$(jq '[.[] | select(.status == "process")] | length' "$JSON_FILE")
            local current_process_count=$((processed_accounts + failed_accounts))
            if [ $current_process_count -lt $process_count ]; then
                log "INFO" "Waiting ${DELAY_MINUTES} minutes before next account..."
                if command -v bc >/dev/null 2>&1; then
                    sleep_time=$(echo "$DELAY_MINUTES * 60" | bc -l)
                else
                    sleep_time=$(awk "BEGIN {print $DELAY_MINUTES * 60}")
                fi
                sleep "$sleep_time"
            fi
        else
            skipped_accounts=$((skipped_accounts + 1))
            log "INFO" "Skipped $email (status: $status)"
        fi

    done

    # Summary
    log "INFO" "=== Processing Summary ==="
    log "INFO" "Total accounts: $total_accounts"
    log "INFO" "Processed successfully: $processed_accounts"
    log "INFO" "Failed: $failed_accounts"
    log "INFO" "Skipped: $skipped_accounts"

    if [ $failed_accounts -eq 0 ]; then
        log "SUCCESS" "All process accounts processed successfully!"
    else
        log "WARNING" "$failed_accounts accounts failed processing"
    fi
}

# Process accounts concurrently (all at once)
process_concurrent() {
    log "INFO" "Starting concurrent processing of all accounts with 'process' status..."

    # Perform daily reset check first
    perform_daily_reset

    local pids=()
    local total_accounts=0

    # Start all process accounts in background
    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.password)|\(.reward)|\(.status)|\(.voucher)"' "$JSON_FILE")

    if [ -z "$account_data" ]; then
        log "ERROR" "No account data found or failed to read JSON file"
        return 1
    fi

    while IFS='|' read -r email password reward status voucher; do
        ((total_accounts++))

        if [ "$status" = "process" ]; then
            log "INFO" "Starting background process for: $email"
            run_account "$email" "$password" "$reward" "$status" "$voucher" &
            pids+=($!)
        else
            log "INFO" "Skipping $email (status: $status)"
        fi

    done <<< "$account_data"

    # Wait for all background processes to complete
    log "INFO" "Waiting for all processes to complete..."
    local failed_count=0

    for pid in "${pids[@]}"; do
        if ! wait "$pid"; then
            ((failed_count++))
        fi
    done

    if [ $failed_count -eq 0 ]; then
        log "SUCCESS" "All concurrent processes completed successfully!"
    else
        log "WARNING" "$failed_count processes failed"
    fi
}

# List all accounts with their current status
list_accounts() {
    log "INFO" "Current account status:"
    echo
    printf "%-35s %-10s %-15s %-10s %-20s\n" "EMAIL" "REWARD" "STATUS" "VOUCHER" "LAST_SUCCESS"
    printf "%-35s %-10s %-15s %-10s %-20s\n" "-----" "------" "------" "-------" "------------"

    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.password)|\(.reward)|\(.status)|\(.voucher)|\(.last_success)"' "$JSON_FILE")

    while IFS='|' read -r email password reward status voucher last_success; do
        # Format last_success for display
        if [ "$last_success" = "null" ] || [ -z "$last_success" ]; then
            last_success="Never"
        else
            # Convert to local time for display
            last_success=$(date -d "$last_success" "+%Y-%m-%d %H:%M" 2>/dev/null || echo "$last_success")
        fi
        printf "%-35s %-10s %-15s %-10s %-20s\n" "$email" "$reward" "$status" "$voucher" "$last_success"
    done <<< "$account_data"
    echo
}

# Reset all account statuses to process
reset_statuses() {
    log "INFO" "Resetting all account statuses to 'process'..."

    # Create backup
    cp "$JSON_FILE" "${JSON_FILE}.backup"

    # Reset all statuses to process and clear last_success timestamps
    jq 'map(.status = "process" | .last_success = null)' "$JSON_FILE" > "${JSON_FILE}.tmp" && mv "${JSON_FILE}.tmp" "$JSON_FILE"

    log "SUCCESS" "All account statuses reset to 'process'"
}

# Reset only successful accounts back to process (manual daily reset)
reset_successful() {
    log "INFO" "Resetting successful accounts back to 'process' status..."

    # Create backup
    cp "$JSON_FILE" "${JSON_FILE}.backup"

    local reset_count=0
    local account_data
    account_data=$(jq -r '.[] | "\(.email)|\(.status)"' "$JSON_FILE")

    while IFS='|' read -r email status; do
        if [ "$status" = "success" ]; then
            update_account_status "$email" "process"
            reset_count=$((reset_count + 1))
        fi
    done <<< "$account_data"

    log "SUCCESS" "Reset $reset_count successful accounts back to process status"
}

# Show usage information
show_usage() {
    cat << EOF
Account Runner Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    run                     Process all accounts with 'process' status sequentially (2min delay)
    run-concurrent          Process all accounts with 'process' status concurrently
    list                    List all accounts with their status, voucher, and last success
    reset                   Reset all account statuses to 'process'
    reset-successful        Reset only successful accounts back to 'process' (manual daily reset)
    daily-reset             Check and perform automatic daily reset for accounts successful 24+ hours

Options:
    --delay MINUTES         Set delay between accounts (default: 2 minutes)
    --json FILE             Specify JSON file to use (default: account.json)

Examples:
    $0 run                                    # Run all process accounts with 2min delay
    $0 run --delay 5                         # Run all process accounts with 5min delay
    $0 run-concurrent                         # Run all process accounts at the same time
    $0 list                                   # Show current account status
    $0 reset                                  # Reset all statuses to process
    $0 reset-successful                       # Reset only successful accounts to process
    $0 daily-reset                           # Perform daily reset check
    $0 run --json account_personal.json      # Use personal accounts file

Status Meanings:
    process     - Account is ready to be processed
    processing  - Account is currently being processed (temporary)
    success     - Account completed successfully (will auto-reset after 24 hours)

EOF
}

# Main function
main() {
    # Parse arguments for delay and JSON file first
    local command=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --delay)
                DELAY_MINUTES="$2"
                shift 2
                ;;
            --json|--file|-f)
                JSON_FILE="$2"
                # Convert relative path to absolute path
                if [[ ! "$JSON_FILE" = /* ]]; then
                    JSON_FILE="${SCRIPT_DIR}/$JSON_FILE"
                fi
                shift 2
                ;;
            run|run-concurrent|list|reset|reset-successful|daily-reset|help|-h|--help)
                command="$1"
                shift
                ;;
            *)
                if [ -z "$command" ]; then
                    command="$1"
                fi
                shift
                ;;
        esac
    done

    # Check requirements
    check_requirements

    # Handle commands
    case "${command:-}" in
        "run")
            process_all_accounts
            ;;
        "run-concurrent")
            process_concurrent
            ;;
        "list")
            list_accounts
            ;;
        "reset")
            reset_statuses
            ;;
        "reset-successful")
            reset_successful
            ;;
        "daily-reset")
            perform_daily_reset
            ;;
        "help"|"-h"|"--help"|"")
            show_usage
            ;;
        *)
            log "ERROR" "Unknown command: ${command:-none}"
            show_usage
            exit 1
            ;;
    esac
}

# Initialize log file
echo "=== Account Runner Started at $(date) ===" >> "$LOG_FILE"

# Run main function with all arguments
main "$@"
