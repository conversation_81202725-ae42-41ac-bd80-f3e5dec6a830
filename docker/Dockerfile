FROM python:3.10-slim-bullseye

# Disable writing .pyc files
ENV PYTHONDONTWRITEBYTECODE=1
# Enable fault handler
ENV PYTHONFAULTHANDLER=1
ENV PYTHONUNBUFFERED=1

# Prevents some interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    libpq-dev \
    netcat \
    sudo

WORKDIR /app

COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt && rm requirements.txt

COPY cli ./cli
COPY src ./src
COPY scripts/canva_execute.sh .
COPY .env .

RUN chmod +x canva_execute.sh

ENTRYPOINT ["./canva_execute.sh"]
CMD ["chrome", "9222"]
