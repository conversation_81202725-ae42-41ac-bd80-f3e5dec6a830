services:
  chrome:
    image: canva-crawler
    container_name: canva-crawler-chrome
    env_file:
      - .env
    volumes:
      - /usr/bin/google-chrome:/usr/bin/google-chrome
      - /home/<USER>/project/canva_resources/templates.txt:/app/canva_resources/templates.txt
      - /home/<USER>/Downloads/canva-dls:/home/<USER>/Downloads/canva-dls
    network_mode: host
    command: ["chrome", "9222"]

  brave:
    image: canva-crawler
    container_name: canva-crawler-brave
    env_file:
      - .env
    volumes:
      - /usr/bin/brave-browser:/usr/bin/brave-browser
      - /home/<USER>/project/canva_resources/templates.txt:/app/canva_resources/templates.txt
      - /home/<USER>/Downloads/canva-dls:/home/<USER>/Downloads/canva-dls
    network_mode: host
    command: ["brave", "9223"]

  edge:
    image: canva-crawler
    container_name: canva-crawler-edge
    env_file:
      - .env
    volumes:
      - /usr/bin/microsoft-edge-dev:/usr/bin/microsoft-edge-dev
      - /home/<USER>/project/canva_resources/templates.txt:/app/canva_resources/templates.txt
      - /home/<USER>/Downloads/canva-dls:/home/<USER>/Downloads/canva-dls
    network_mode: host
    command: [ "edge", "9224" ]
