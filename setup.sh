#!/bin/bash

# Setup script for Account Manager System
# This script prepares the environment and installs dependencies

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    
    case "$level" in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" >&2
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Detect operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            echo "debian"
        elif [ -f /etc/redhat-release ]; then
            echo "redhat"
        else
            echo "linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]]; then
        echo "windows"
    else
        echo "unknown"
    fi
}

# Install jq based on OS
install_jq() {
    local os=$(detect_os)
    
    log "INFO" "Installing jq for $os..."
    
    case "$os" in
        "debian")
            if command -v apt-get &> /dev/null; then
                sudo apt-get update
                sudo apt-get install -y jq
            else
                log "ERROR" "apt-get not found. Please install jq manually."
                return 1
            fi
            ;;
        "redhat")
            if command -v yum &> /dev/null; then
                sudo yum install -y jq
            elif command -v dnf &> /dev/null; then
                sudo dnf install -y jq
            else
                log "ERROR" "yum/dnf not found. Please install jq manually."
                return 1
            fi
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew install jq
            else
                log "ERROR" "Homebrew not found. Please install jq manually or install Homebrew first."
                return 1
            fi
            ;;
        "windows")
            log "WARNING" "Windows detected. Please install jq manually from https://stedolan.github.io/jq/download/"
            return 1
            ;;
        *)
            log "ERROR" "Unknown OS. Please install jq manually."
            return 1
            ;;
    esac
    
    log "SUCCESS" "jq installed successfully"
}

# Check and install dependencies
check_dependencies() {
    log "INFO" "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log "WARNING" "Missing dependencies: ${missing_deps[*]}"
        
        for dep in "${missing_deps[@]}"; do
            case "$dep" in
                "jq")
                    read -p "Install jq? (y/n): " -n 1 -r
                    echo
                    if [[ $REPLY =~ ^[Yy]$ ]]; then
                        install_jq
                    else
                        log "ERROR" "jq is required for the account manager to work"
                        return 1
                    fi
                    ;;
            esac
        done
    else
        log "SUCCESS" "All dependencies are installed"
    fi
}

# Make scripts executable
make_executable() {
    log "INFO" "Making scripts executable..."
    
    local scripts=("account_manager.sh" "account_utils.sh")
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            chmod +x "$script"
            log "SUCCESS" "Made $script executable"
        else
            log "WARNING" "Script not found: $script"
        fi
    done
}

# Create necessary directories
create_directories() {
    log "INFO" "Creating necessary directories..."
    
    local dirs=("backups" "logs")
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log "SUCCESS" "Created directory: $dir"
        else
            log "INFO" "Directory already exists: $dir"
        fi
    done
}

# Validate configuration files
validate_files() {
    log "INFO" "Validating configuration files..."
    
    # Check JSON file
    if [ -f "account.json" ]; then
        if jq empty account.json 2>/dev/null; then
            log "SUCCESS" "account.json is valid"
        else
            log "ERROR" "account.json is invalid JSON"
            return 1
        fi
    else
        log "WARNING" "account.json not found"
    fi
    
    # Check CSV file
    if [ -f "accounts.csv" ]; then
        if [ -s "accounts.csv" ]; then
            log "SUCCESS" "accounts.csv exists and is not empty"
        else
            log "WARNING" "accounts.csv is empty"
        fi
    else
        log "WARNING" "accounts.csv not found"
    fi
}

# Show system information
show_system_info() {
    log "INFO" "System Information:"
    echo "  OS: $(detect_os)"
    echo "  Shell: $SHELL"
    echo "  User: $USER"
    echo "  Working Directory: $(pwd)"
    echo "  jq Version: $(jq --version 2>/dev/null || echo 'Not installed')"
    echo
}

# Main setup function
main() {
    log "INFO" "Starting Account Manager System setup..."
    echo
    
    show_system_info
    check_dependencies
    make_executable
    create_directories
    validate_files
    
    echo
    log "SUCCESS" "Setup completed successfully!"
    echo
    log "INFO" "You can now use the account manager with:"
    log "INFO" "  ./account_manager.sh list          # List all accounts"
    log "INFO" "  ./account_manager.sh process       # Process accounts"
    log "INFO" "  ./account_utils.sh stats           # Show statistics"
    log "INFO" "  ./account_utils.sh backup          # Create backup"
    echo
    log "INFO" "For more commands, run:"
    log "INFO" "  ./account_manager.sh help"
    log "INFO" "  ./account_utils.sh help"
}

# Run main function
main "$@"
