import argparse

from faker import Faker
from loguru import logger
from tqdm import tqdm
from DrissionPage import Chromium
from DrissionPage.common import Keys
from DrissionPage.errors import ElementNotFoundError

from base.drission_page_base import DrissionPageBase


def get_arguments():

    parser = argparse.ArgumentParser(prog='bing_reward_bot',
                                     description="Automatically collect points from Bing Rewards.")

    parser.add_argument("--driver",
                        help="The type of chromedriver.",
                        choices=["chrome", "firefox", "opera", "brave", "edge"],
                        default="chrome")

    parser.add_argument("--port",
                        help="The port of browser debugger.",
                        type=int,
                        default=9222)

    parser.add_argument("--times",
                        help="The times of search.",
                        type=int,
                        default=15)

    parser.add_argument("--email",
                        help="The email of Bing Rewards.",
                        type=str,
                        default="")

    parser.add_argument("--password",
                        help="The password of Bing Rewards.",
                        type=str,
                        default="")

    parser.add_argument("--headless",
                        help="Run browser in headless mode.",
                        action="store_true")

    parser.add_argument("--manual",
                        help="Skip login.",
                        action="store_true")

    args = parser.parse_args()
    return args


def login(email: str = None, password: str = None):
    logger.info("Start login...")
    obj.open_url("https://rewards.bing.com")
    mail_input = obj.page('x://input[@type="email"]')
    obj.sleep_for_seconds(2)
    mail_input.input(email)
    obj.sleep_for_seconds(1)
    obj.page.actions.key_down(Keys.ENTER)
    try:
        others_btn = obj.page('x:div[span[text()="Other ways to sign in"]]', timeout=5)
        obj.sleep_for_seconds(1)
        tab = Chromium().latest_tab
        tab.actions.move_to(others_btn).click()
        obj.sleep_for_seconds(2)
    except ElementNotFoundError:
        obj.sleep_for_seconds(1)
    try:
        use_pw_btn = obj.page('x://div[span[text()="Use your password"]]', timeout=5)
        obj.sleep_for_seconds(1)
        tab = Chromium().latest_tab
        tab.actions.move_to(use_pw_btn).click()
        obj.sleep_for_seconds(2)
    except ElementNotFoundError:
        obj.sleep_for_seconds(1)
    password_input = obj.page('x://input[@type="password"]')
    password_input.input(password)
    obj.sleep_for_seconds(1)
    obj.page.actions.key_down(Keys.ENTER)
    obj.sleep_for_seconds(3)
    obj.page('x://button[text()="No"]').click(by_js=True)
    obj.sleep_for_seconds(3)
    obj.open_url("https://www.bing.com/")
    obj.sleep_for_seconds(3)
    bing_signin_btn = obj.page('#id_l')
    tab = Chromium().latest_tab
    tab.actions.move_to(bing_signin_btn).click()
    obj.sleep_for_seconds(3)
    logger.info(f"Login success with email: {email}")


def logout():
    logger.info("Start logout...")
    obj.open_url("https://rewards.bing.com/Signout")
    obj.sleep_for_seconds(5)
    logger.info("Logout success")


def search(times: int = 35):
    logger.info("Start searching...")
    obj.open_url("https://rewards.bing.com")
    search_input = obj.page('#rewards-suggestedSearch-searchbox')
    search_button = obj.page('x://div[@role="link"]')
    for _ in tqdm(range(times)):
        keyword = fake.word()
        search_input.input(keyword, by_js=True, clear=True)
        obj.sleep_for_seconds(1)
        search_button.click(by_js=True)
        obj.sleep_for_seconds(fake.random_int(min=5, max=10))
    obj.sleep_for_seconds(15)
    logger.info("Search finished")

def click_banner():
    logger.info("Start clicking banner...")
    obj.open_url("https://rewards.bing.com")
    daily_sets = obj.page.eles('x://div[@id="daily-sets"]//mee-card[not(@disabled)]//a')
    activities = obj.page.eles('x://div[@id="more-activities"]//mee-card//a')
    for daily_set in tqdm(daily_sets):
        daily_set.click(by_js=True)
        obj.sleep_for_seconds(5)
    for activity in tqdm(activities):
        activity.click(by_js=True)
        obj.sleep_for_seconds(5)
    obj.sleep_for_seconds(10)
    logger.info("Click banner finished")


if __name__ == '__main__':
    fake = Faker()

    args = get_arguments()
    driver = args.driver
    port = args.port
    times = args.times
    email = args.email
    password = args.password
    headless = args.headless
    manual = args.manual

    obj = DrissionPageBase(is_incognito=not manual, is_headless=headless, driver_name=driver, debug_port=port)

    if not manual:
        if not email or not password:
            logger.error("Please input email and password.")
            exit(1)
        else:
            logout()
            login(email=email, password=password)

    click_banner()
    search(times=times)
    logout()
    obj.quit()

