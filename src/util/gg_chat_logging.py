import json
import decouple

from httplib2 import Http


GOOGLE_WEBHOOK_URL = decouple.config("GOOGLE_WEBHOOK_URL")


def post_message(message: str):
    url = GOOGLE_WEBHOOK_URL
    payload = {"text": message}

    headers = {"Content-Type": "application/json; charset=UTF-8"}

    http_obj = Http()
    response, content = http_obj.request(
        uri=url,
        method="POST",
        headers=headers,
        body=json.dumps(payload),
    )

    # Optional: parse and return thread info
    if response.status != 200:
        raise Exception(f"Failed to send message: {response.status} - {content.decode()}")


if __name__ == "__main__":
    post_message("test")
